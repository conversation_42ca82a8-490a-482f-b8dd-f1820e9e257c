package dao_cdk

import (
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/go-redis/redis/v8"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_cdk"
)

func getMysqlEngine() (*xorm.Engine, error) {
	return mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
}

func getDbRunner(existingSessions ...*xorm.Session) (xorm.Interface, error) {
	if len(existingSessions) > 0 && existingSessions[0] != nil {
		return existingSessions[0], nil
	}
	return getMysqlEngine()
}

// CreateCDKBatch 插入一条新的 CDKBatch 记录到数据库
func CreateCDKBatch(ctx context.Context, batch *model_cdk.CDKBatch, tx ...*xorm.Session) error {
	db, err := getDbRunner(tx...)
	if err != nil {
		return fmt.Errorf("CreateCDKBatch: %w", err)
	}

	_, err = db.Table(config.TableCDKBatch).InsertOne(batch)
	if err != nil {
		return fmt.Errorf("failed to insert CDKBatch: %w", err)
	}

	return nil
}

// CreateCDKRecords 批量插入 CDKRecord 记录到数据库
func CreateCDKRecords(ctx context.Context, cdkRecords []*model_cdk.CDKRecord, tx ...*xorm.Session) error {
	entry := logx.NewLogEntry(ctx)
	if len(cdkRecords) == 0 {
		return nil
	}

	// 检查是否在有效的事务中调用
	if len(tx) == 0 || tx[0] == nil {
		return fmt.Errorf("CreateCDKRecords: must be called within an existing transaction")
	}
	sessionToUse := tx[0] // Use the provided session directly

	insertedCount, err := sessionToUse.Table(config.TableCDKRecord).InsertMulti(cdkRecords)
	if err != nil {
		// 错误将由调用者（事务的管理者）处理，包括回滚。
		return fmt.Errorf("failed to batch insert CDKRecords: %w", err)
	}

	entry.Debugf("batch insert records: count=%d", insertedCount)
	return nil
}

// CreateCDKBatchWithRecords 在单个事务中创建 CDK 批次和对应的 CDK 记录。
// 如果任何操作失败，整个事务将回滚。
func CreateCDKBatchWithRecords(ctx context.Context, batch *model_cdk.CDKBatch, records []*model_cdk.CDKRecord) (err error) {
	entry := logx.NewLogEntry(ctx)

	engine, dbErr := getMysqlEngine()
	if engine == nil || dbErr != nil {
		logrus.Errorf("mysql table engine is empty")
		return
	}

	session := engine.NewSession()
	defer session.Close()

	if err = session.Begin(); err != nil {
		logrus.Errorf("failed to begin transaction: %v", err)
		return
	}

	// 1. 创建 CDKBatch
	if err = CreateCDKBatch(ctx, batch, session); err != nil {
		entry.Errorf("create batch failed: err=%v", err)
		return err
	}

	// 2. 创建 CDKRecords
	if len(records) > 0 {
		for _, rec := range records {
			rec.BatchID = batch.ID
		}
		if err = CreateCDKRecords(ctx, records, session); err != nil {
			entry.Errorf("create records failed: err=%v", err)
			return err
		}
	}

	// 3. 提交事务
	if err = session.Commit(); err != nil {
		entry.Errorf("commit transaction failed: err=%v", err)
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	entry.Infof("create batch with records success: id=%d count=%d", batch.ID, len(records))
	return nil
}

func GetCdkBatchInfoByCdk(ctx context.Context, cdkCode string) (*model_cdk.CdkBatchRecordResult, error) {
	entry := logx.NewLogEntry(ctx)

	// 先尝试从缓存获取
	cacheResult, err := GetCdkInfoFromCache(ctx, cdkCode)
	if err == nil && cacheResult != nil {
		return cacheResult, nil
	}
	if err != nil && !errors.Is(err, redis.Nil) {
		entry.Warnf("cache get failed: cdk=%s err=%v", cdkCode, err)
	}

	// 缓存未命中，从数据库查询
	db, err := getDbRunner()
	if err != nil {
		return nil, err
	}
	var result model_cdk.CdkBatchRecordResult

	// 使用ORM方式进行JOIN查询
	has, err := db.Table(config.TableCDKBatch).Alias("tcb").
		Join("INNER", []string{config.TableCDKRecord, "tcr"}, "tcr.batch_id = tcb.id").
		Select("tcb.channel_id, tcr.cdk, tcb.start_time, tcb.end_time, tcb.cdk_limit, tcb.rewards, tcb.status, tcr.used_bm, tcr.used_count").
		Where("tcr.cdk = ?", cdkCode).
		Get(&result)
	if err != nil {
		return nil, err
	}
	if !has {
		// 使用model层方法构造无效CDK结果
		result = *model_cdk.CreateInvalidCdkResult(cdkCode)
	}

	// 将查询结果存入缓存
	err = SetCdkInfoToCache(ctx, cdkCode, &result)
	if err != nil {
		// 缓存失败只记录日志，不影响主流程
		entry.Warnf("cache set failed: cdk=%s err=%v", cdkCode, err)
	}

	return &result, nil
}

// UpdateCdkRecordUsage 根据CDK码更新 t_cdk_record 表中特定记录的 used_bm 字段，并将 used_count 自增1。
func UpdateCdkRecordUsage(ctx context.Context, cdkCode string, usedBmValue string) error {
	entry := logx.NewLogEntry(ctx)

	err := DeleteCdkInfoFromCache(ctx, cdkCode)
	if err != nil {
		entry.Warnf("cache delete failed: cdk=%s err=%v", cdkCode, err)
		return err
	}

	db, err := getDbRunner()
	if err != nil {
		return fmt.Errorf("UpdateCdkRecordUsage: failed to get db runner: %w", err)
	}

	// 使用ORM方式更新记录
	affected, err := db.Table(config.TableCDKRecord).
		Where("cdk = ?", cdkCode).
		Incr("used_count", 1).
		Update(&model_cdk.CDKRecord{UsedBM: usedBmValue})
	if err != nil {
		entry.Errorf("update record failed: cdk=%s err=%v", cdkCode, err)
		return fmt.Errorf("failed to update t_cdk_record for cdk %s: %w", cdkCode, err)
	}

	entry.Debugf("update record success: cdk=%s affected=%d", cdkCode, affected)
	return nil
}

// QueryCDKBatches 分页查询CDK批次
func QueryCDKBatches(ctx context.Context, page, pageSize int, status int8) ([]*model_cdk.CDKBatch, int64, error) {
	entry := logx.NewLogEntry(ctx)

	db, err := getDbRunner()
	if err != nil {
		return nil, 0, fmt.Errorf("QueryCDKBatches: failed to get db runner: %w", err)
	}

	// 构建查询条件
	session := db.Table(config.TableCDKBatch)
	if status != 0 {
		session = session.Where("status = ?", status)
	}

	// 查询总数
	total, err := session.Count(&model_cdk.CDKBatch{})
	if err != nil {
		entry.Errorf("count batches failed: err=%v", err)
		return nil, 0, fmt.Errorf("failed to count CDK batches: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var batches []*model_cdk.CDKBatch
	err = session.OrderBy("created_at DESC").Limit(pageSize, offset).Find(&batches)
	if err != nil {
		entry.Errorf("query batches failed: err=%v", err)
		return nil, 0, fmt.Errorf("failed to query CDK batches: %w", err)
	}

	return batches, total, nil
}

// QueryCDKRecords 分页查询指定批次的CDK记录
func QueryCDKRecords(ctx context.Context, batchID uint64, page, pageSize int) ([]*model_cdk.CDKRecord, int64, error) {
	entry := logx.NewLogEntry(ctx)

	db, err := getDbRunner()
	if err != nil {
		return nil, 0, fmt.Errorf("QueryCDKRecords: failed to get db runner: %w", err)
	}

	// 构建查询条件
	session := db.Table(config.TableCDKRecord).Where("batch_id = ?", batchID)

	// 查询总数
	total, err := session.Count(&model_cdk.CDKRecord{})
	if err != nil {
		entry.Errorf("count records failed: batch=%d err=%v", batchID, err)
		return nil, 0, fmt.Errorf("failed to count CDK records: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var records []*model_cdk.CDKRecord
	err = session.OrderBy("created_at DESC").Limit(pageSize, offset).Find(&records)
	if err != nil {
		entry.Errorf("query records failed: batch=%d err=%v", batchID, err)
		return nil, 0, fmt.Errorf("failed to query CDK records: %w", err)
	}

	return records, total, nil
}

// DisableCDKBatch 作废CDK批次
func DisableCDKBatch(ctx context.Context, batchID uint64) error {
	entry := logx.NewLogEntry(ctx)

	db, err := getDbRunner()
	if err != nil {
		return fmt.Errorf("DisableCDKBatch: failed to get db runner: %w", err)
	}

	// 使用ORM方式更新批次状态为作废
	affected, err := db.Table(config.TableCDKBatch).
		Where("id = ?", batchID).
		Update(&model_cdk.CDKBatch{Status: model_cdk.CDKStatusInvalid})
	if err != nil {
		entry.Errorf("disable batch failed: id=%d err=%v", batchID, err)
		return fmt.Errorf("failed to disable CDK batch: %w", err)
	}

	entry.Debugf("disable batch success: id=%d affected=%d", batchID, affected)
	return nil
}
