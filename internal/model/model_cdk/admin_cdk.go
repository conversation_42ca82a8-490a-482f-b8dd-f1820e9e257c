package model_cdk

import (
	"encoding/json"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	"github.com/sirupsen/logrus"
)

func GetRewardsStr(rewards []*commonPB.ItemBase) string {
	if rewards == nil {
		return ""
	}
	jsonRewards, marshalErr := json.Marshal(rewards)
	if marshalErr != nil {
		logrus.Errorf("failed to marshal rewards: %s", marshalErr)
		return ""
	}
	return string(jsonRewards)
}

// ToCDKBatch 将输入参数转换为CDKBatch对象
func ToCDKBatch(c *gmPB.GmCmdCreateCDKReq) *CDKBatch {
	if c == nil {
		return nil
	}

	startTime := time.Unix(c.StartTime, 0)
	endTime := time.Unix(c.EndTime, 0)
	now := time.Now()

	return &CDKBatch{
		ChannelID:        c.ChannelId,
		Description:      c.Description,
		GenerationOption: int8(c.GenerationOption),
		StartTime:        startTime,
		EndTime:          endTime,
		CDKLimit:         c.CdkUseLimit,
		Rewards:          GetRewardsStr(c.Rewards),
		Status:           CDKStatusValid, // 默认为有效状态
		CreatedAt:        now,
		UpdatedAt:        now,
	}
}

// CreateCDKRecords 根据CDK码列表创建CDKRecord对象列表
func CreateCDKRecords(cdkCodes []string) []*CDKRecord {
	if len(cdkCodes) == 0 {
		return nil
	}

	now := time.Now()
	records := make([]*CDKRecord, 0, len(cdkCodes))

	for _, code := range cdkCodes {
		record := &CDKRecord{
			CDK:       code,
			CreatedAt: now,
			UpdatedAt: now,
		}
		records = append(records, record)
	}

	return records
}

// CDK批次查询请求结构体
type QueryCDKBatchesRequest struct {
	Page     int  `json:"page" binding:"required,min=1"`      // 页码，从1开始
	PageSize int  `json:"page_size" binding:"required,min=1"` // 每页数量
	Status   int8 `json:"status,omitempty"`                   // 状态筛选，可选
}

// CDK批次查询响应结构体
type QueryCDKBatchesResponse struct {
	Total    int64              `json:"total"`     // 总数
	Page     int                `json:"page"`      // 当前页码
	PageSize int                `json:"page_size"` // 每页数量
	Batches  []*CDKBatchSummary `json:"batches"`   // 批次列表
}

// CDK批次摘要信息
type CDKBatchSummary struct {
	ID          uint64    `json:"id"`          // 批次ID
	ChannelID   int32     `json:"channel_id"`  // 渠道ID
	Description string    `json:"description"` // 批次描述
	CDKCount    int32     `json:"cdk_count"`   // CDK数量
	Status      int8      `json:"status"`      // 状态
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
}

// CDK记录查询请求结构体
type QueryCDKRecordsRequest struct {
	BatchID  uint64 `json:"batch_id" binding:"required"`        // 批次ID
	Page     int    `json:"page" binding:"required,min=1"`      // 页码，从1开始
	PageSize int    `json:"page_size" binding:"required,min=1"` // 每页数量
}

// CDK记录查询响应结构体
type QueryCDKRecordsResponse struct {
	Total    int64            `json:"total"`     // 总数
	Page     int              `json:"page"`      // 当前页码
	PageSize int              `json:"page_size"` // 每页数量
	Records  []*CDKRecordInfo `json:"records"`   // CDK记录列表
}

// CDK记录信息
type CDKRecordInfo struct {
	ID        uint64    `json:"id"`         // 记录ID
	CDK       string    `json:"cdk"`        // CDK码
	UsedCount int32     `json:"used_count"` // 使用次数
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// CDK批次作废请求结构体
type DisableCDKBatchRequest struct {
	BatchID    uint64 `json:"batch_id" binding:"required"` // 批次ID
	OperatorID uint64 `json:"operator_id,omitempty"`       // 操作人ID，可选
}

// CDK批次作废响应结构体
type DisableCDKBatchResponse struct {
	Success bool   `json:"success"` // 操作是否成功
	Message string `json:"message"` // 操作结果消息
}

// FromGmQueryCDKBatchesReq 将 gmPB 查询请求转换为 model_cdk 请求
func FromGmQueryCDKBatchesReq(req *gmPB.GmCmdQueryCDKBatchesReq) *QueryCDKBatchesRequest {
	if req == nil {
		return nil
	}

	page := 1
	pageSize := 10
	if req.Pagination != nil {
		page = int(req.Pagination.PageIndex)
		pageSize = int(req.Pagination.PageSize)
	}

	return &QueryCDKBatchesRequest{
		Page:     page,
		PageSize: pageSize,
		Status:   int8(req.Status),
	}
}

// ToGmQueryCDKBatchesRsp 将 model_cdk 响应转换为 gmPB 响应
func ToGmQueryCDKBatchesRsp(resp *QueryCDKBatchesResponse) *gmPB.GmCmdQueryCDKBatchesRsp {
	if resp == nil {
		return nil
	}

	batches := make([]*gmPB.CDKBatchSummary, 0, len(resp.Batches))
	for _, batch := range resp.Batches {
		gmBatch := &gmPB.CDKBatchSummary{
			Id:          batch.ID,
			ChannelId:   commonPB.CHANNEL_TYPE(batch.ChannelID),
			Description: batch.Description,
			CdkCount:    batch.CDKCount,
			Status:      commonPB.CDK_BATCH_STATUS(batch.Status),
			CreatedAt:   batch.CreatedAt.Unix(),
			UpdatedAt:   batch.UpdatedAt.Unix(),
		}
		batches = append(batches, gmBatch)
	}

	pagination := &commonPB.PaginationReq{
		PageIndex: int32(resp.Page),
		PageSize:  int32(resp.PageSize),
	}

	return &gmPB.GmCmdQueryCDKBatchesRsp{
		Total:      resp.Total,
		Pagination: pagination,
		Batches:    batches,
	}
}

// FromGmQueryCDKRecordsReq 将 gmPB 查询记录请求转换为 model_cdk 请求
func FromGmQueryCDKRecordsReq(req *gmPB.GmCmdQueryCDKRecordsReq) *QueryCDKRecordsRequest {
	if req == nil {
		return nil
	}

	return &QueryCDKRecordsRequest{
		BatchID:  req.BatchId,
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
	}
}

// ToGmQueryCDKRecordsRsp 将 model_cdk 响应转换为 gmPB 响应
func ToGmQueryCDKRecordsRsp(resp *QueryCDKRecordsResponse) *gmPB.GmCmdQueryCDKRecordsRsp {
	if resp == nil {
		return nil
	}

	records := make([]*gmPB.CDKRecordInfo, 0, len(resp.Records))
	for _, record := range resp.Records {
		gmRecord := &gmPB.CDKRecordInfo{
			Id:        record.ID,
			Cdk:       record.CDK,
			UsedCount: record.UsedCount,
			CreatedAt: record.CreatedAt.Unix(),
			UpdatedAt: record.UpdatedAt.Unix(),
		}
		records = append(records, gmRecord)
	}

	pagination := &commonPB.PaginationReq{
		PageIndex: int32(resp.Page),
		PageSize:  int32(resp.PageSize),
	}

	return &gmPB.GmCmdQueryCDKRecordsRsp{
		Total:      resp.Total,
		Pagination: pagination,
		Records:    records,
	}
}

// FromGmDisableCDKBatchReq 将 gmPB 禁用请求转换为 model_cdk 请求
func FromGmDisableCDKBatchReq(req *gmPB.GmCmdDisableCDKBatchReq) *DisableCDKBatchRequest {
	if req == nil {
		return nil
	}

	return &DisableCDKBatchRequest{
		BatchID:    req.BatchId,
		OperatorID: req.OperatorId,
	}
}

// ToGmDisableCDKBatchRsp 将 model_cdk 响应转换为 gmPB 响应
func ToGmDisableCDKBatchRsp(resp *DisableCDKBatchResponse) *gmPB.GmCmdDisableCDKBatchRsp {
	if resp == nil {
		return nil
	}
	var ret *commonPB.Result
	if resp.Success {
		ret = &commonPB.Result{
			Code: commonPB.ErrCode_ERR_SUCCESS,
			Desc: resp.Message,
		}
	} else {
		ret = &commonPB.Result{
			Code: commonPB.ErrCode_ERR_UNKNOWN,
			Desc: resp.Message,
		}
	}

	return &gmPB.GmCmdDisableCDKBatchRsp{
		Ret: ret,
	}
}
