package main

import (
	"fmt"
	"reflect"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func main() {
	// 查看 PaginationReq 结构体字段
	pagination := &commonPB.PaginationReq{}
	paginationType := reflect.TypeOf(pagination).Elem()
	fmt.Println("PaginationReq fields:")
	for i := 0; i < paginationType.NumField(); i++ {
		field := paginationType.Field(i)
		fmt.Printf("  %s: %s\n", field.Name, field.Type)
	}

	// 查看 Result 结构体字段
	result := &commonPB.Result{}
	resultType := reflect.TypeOf(result).Elem()
	fmt.Println("\nResult fields:")
	for i := 0; i < resultType.NumField(); i++ {
		field := resultType.Field(i)
		fmt.Printf("  %s: %s\n", field.Name, field.Type)
	}

	// 查看 ErrCode 枚举值
	fmt.Println("\nErrCode values:")
	fmt.Printf("  ERR_SUCCESS: %v\n", commonPB.ErrCode_ERR_SUCCESS)
}
